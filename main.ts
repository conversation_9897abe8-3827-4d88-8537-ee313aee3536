import fetch from 'node-fetch';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import * as readline from 'readline';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuration
const SPOTIFY_CLIENT_ID = process.env.SPOTIFY_CLIENT_ID || "";
const SPOTIFY_CLIENT_SECRET = process.env.SPOTIFY_CLIENT_SECRET || "";
const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY || "";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const DATA_FILE = path.join(__dirname, "last_releases.json");

// Interfaces
interface Release {
  id: string;
  name: string;
  release_date: string;
}

interface Video {
  id: string;
  title: string;
  published_at: string;
}

interface LastReleases {
  spotify: { [artistId: string]: Release };
  youtube: { [channelId: string]: Video };
}

// Load or initialize last checked releases
const loadLastReleases = (): LastReleases => {
  if (fs.existsSync(DATA_FILE)) {
    return JSON.parse(fs.readFileSync(DATA_FILE, 'utf-8')) as LastReleases;
  }
  return { spotify: {}, youtube: {} };
};

// Save last checked releases
const saveLastReleases = (data: LastReleases): void => {
  fs.writeFileSync(DATA_FILE, JSON.stringify(data));
};

// Get Spotify access token
const getSpotifyToken = async (): Promise<string> => {
  const response = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Basic ${Buffer.from(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`).toString('base64')}`,
    },
    body: 'grant_type=client_credentials',
  });
  const data = await response.json();
  return data.access_token;
};

// Check Spotify for new releases
const checkSpotifyNewReleases = async (artistId: string, token: string): Promise<Release | null> => {
  const response = await fetch(`https://api.spotify.com/v1/artists/${artistId}/albums?album_type=album,single&limit=1`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  const data = await response.json();
  const latestRelease = data.items[0];
  if (!latestRelease) return null;
  return {
    id: latestRelease.id,
    name: latestRelease.name,
    release_date: latestRelease.release_date,
  };
};

// Check YouTube for new videos
const checkYouTubeNewVideos = async (channelId: string): Promise<Video | null> => {
  const response = await fetch(
    `https://www.googleapis.com/youtube/v3/search?part=snippet&channelId=${channelId}&maxResults=1&order=date&key=${YOUTUBE_API_KEY}`
  );
  const data = await response.json();
  const latestVideo = data.items[0];
  if (!latestVideo) return null;
  return {
    id: latestVideo.id.videoId,
    title: latestVideo.snippet.title,
    published_at: latestVideo.snippet.publishedAt,
  };
};


// Console notify
const notify = (title: string, message: string): void => {
  console.log(`\n=== ${title} ===\n${message}\n`);
};

// Console menu
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Rachie Spotify: 2SW5wli9uAsYHQnezGESFB
// Will Stetson YouTube: UCy2vGg6LqLkFZrJ1WQ2gG6g
const spotifyArtists = ["2SW5wli9uAsYHQnezGESFB"];
const youtubeChannels = ["UCy2vGg6LqLkFZrJ1WQ2gG6g"];

const printReleases = (lastReleases: LastReleases, sortBy: 'name' | 'date' = 'date') => {
  console.log("\nSpotify Releases:");
  const sortedSpotify = Object.values(lastReleases.spotify).sort((a, b) => {
    return sortBy === 'date'
      ? b.release_date.localeCompare(a.release_date)
      : a.name.localeCompare(b.name);
  });
  sortedSpotify.forEach(r => {
    console.log(`- ${r.name} (${r.release_date}) [${r.id}]`);
  });

  console.log("\nYouTube Videos:");
  const sortedYoutube = Object.values(lastReleases.youtube).sort((a, b) => {
    return sortBy === 'date'
      ? b.published_at.localeCompare(a.published_at)
      : a.title.localeCompare(b.title);
  });
  sortedYoutube.forEach(v => {
    console.log(`- ${v.title} (${v.published_at}) [${v.id}]`);
  });
};

// Main check function for releases
const checkForNewReleases = async (): Promise<boolean> => {
  let lastReleases = loadLastReleases();
  let hasNewReleases = false;

  try {
    const spotifyToken = await getSpotifyToken();

    // Check Spotify
    for (const artistId of spotifyArtists) {
      const release = await checkSpotifyNewReleases(artistId, spotifyToken);
      if (release && (!lastReleases.spotify[artistId] || lastReleases.spotify[artistId].id !== release.id)) {
        notify("🎵 New Spotify Release", `${release.name} released on ${release.release_date}\nSpotify ID: ${release.id}`);
        lastReleases.spotify[artistId] = release;
        hasNewReleases = true;
      }
    }

    // Check YouTube
    for (const channelId of youtubeChannels) {
      const video = await checkYouTubeNewVideos(channelId);
      if (video && (!lastReleases.youtube[channelId] || lastReleases.youtube[channelId].id !== video.id)) {
        notify("🎬 New YouTube Video", `${video.title} uploaded on ${video.published_at}\nVideo URL: https://www.youtube.com/watch?v=${video.id}`);
        lastReleases.youtube[channelId] = video;
        hasNewReleases = true;
      }
    }

    saveLastReleases(lastReleases);

    if (!hasNewReleases) {
      console.log("✅ No new releases found.");
    }

  } catch (error) {
    console.error("❌ Error checking for releases:", error);
    return false;
  }

  return hasNewReleases;
};

const menu = async () => {
  let lastReleases = loadLastReleases();
  while (true) {
    console.log("\n==== Artist Releases Downloader ====");
    console.log("1. View releases (by date)");
    console.log("2. View releases (by name)");
    console.log("3. Update releases");
    console.log("4. Exit");
    const answer = await new Promise<string>(resolve => rl.question("Select an option: ", resolve));
    if (answer === '1') {
      printReleases(lastReleases, 'date');
    } else if (answer === '2') {
      printReleases(lastReleases, 'name');
    } else if (answer === '3') {
      await checkForNewReleases();
      lastReleases = loadLastReleases(); // Reload after update
    } else if (answer === '4') {
      rl.close();
      break;
    } else {
      console.log("Invalid option. Try again.");
    }
  }
};

// Check if running directly or as module
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  // Check for command line arguments
  const args = process.argv.slice(2);
  if (args.includes('--check') || args.includes('-c')) {
    // Run immediate check and exit
    checkForNewReleases().then(() => process.exit(0));
  } else {
    // Run interactive menu
    menu();
  }
}