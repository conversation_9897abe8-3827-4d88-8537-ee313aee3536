# Artist Releases Downloader

A Node.js application that monitors artists for new releases on YouTube and Spotify by checking periodically.

## Features

- 🎵 **Spotify Integration**: Monitor artists for new albums and singles
- 🎬 **YouTube Integration**: Monitor channels for new video uploads
- ⏰ **Daily Monitoring**: Automated daily checks with logging
- 🔍 **On-Demand Checks**: Manual release checking
- 📝 **Persistent Storage**: Tracks last known releases to detect new ones
- 🎯 **Detailed Notifications**: Shows release details with direct links

## Current Artists Being Monitored

- **Rachie** (Spotify): [Profile](https://open.spotify.com/artist/2SW5wli9uAsYHQnezGESFB)
- **Will <PERSON>on** (YouTube): [@willstetson](https://www.youtube.com/@willstetson)

## Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Spotify API Credentials
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here

# YouTube API Key
YOUTUBE_API_KEY=your_youtube_api_key_here
```

### 3. Getting API Keys

#### Spotify API Setup

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Log in with your Spotify account
3. Click "Create App"
4. Fill in the app details:
   - App name: "Artist Releases Monitor" (or any name)
   - App description: "Monitor artists for new releases"
   - Redirect URI: `http://localhost:3000` (not used but required)
5. Accept the terms and create the app
6. Copy the **Client ID** and **Client Secret** to your `.env` file

#### YouTube API Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the YouTube Data API v3:
   - Go to "APIs & Services" > "Library"
   - Search for "YouTube Data API v3"
   - Click on it and press "Enable"
4. Create credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "API Key"
   - Copy the API key to your `.env` file
5. (Optional) Restrict the API key to YouTube Data API v3 for security

## Usage

### Interactive Menu Mode

Run the interactive menu to view releases and manually check for updates:

```bash
npm start
```

### On-Demand Check

Check for new releases immediately and exit:

```bash
npm run check-now
```

### Daily Monitoring

Start the daily monitoring service that checks every 24 hours:

```bash
npm run daily-monitor
```

The daily monitor will:
- Run an initial check immediately
- Schedule subsequent checks every 24 hours
- Log all activity to `monitor.log`
- Continue running until stopped with Ctrl+C

## Files

- `main.ts` - Interactive menu application
- `check-now.ts` - On-demand release checker
- `daily-monitor.ts` - Daily monitoring service
- `last_releases.json` - Stores the last known releases (auto-generated)
- `monitor.log` - Daily monitoring logs (auto-generated)

## How It Works

1. **Spotify**: Uses the Spotify Web API to fetch the latest album/single for each monitored artist
2. **YouTube**: Uses the YouTube Data API v3 to fetch the latest video from each monitored channel
3. **Detection**: Compares the latest releases with previously stored data to detect new releases
4. **Notifications**: Displays detailed information about new releases including direct links

## Adding More Artists

To monitor additional artists, edit the arrays in the respective script files:

```typescript
// For Spotify artists, add their Spotify Artist ID
const spotifyArtists = ["2SW5wli9uAsYHQnezGESFB", "new_artist_id_here"];

// For YouTube channels, add their Channel ID
const youtubeChannels = ["UCy2vGg6LqLkFZrJ1WQ2gG6g", "new_channel_id_here"];
```

### Finding Artist/Channel IDs

- **Spotify Artist ID**: Found in the Spotify URL (e.g., `https://open.spotify.com/artist/2SW5wli9uAsYHQnezGESFB`)
- **YouTube Channel ID**: Can be found in the channel URL or by using online tools to convert @username to channel ID

## Troubleshooting

### Common Issues

1. **API Rate Limits**: Both APIs have rate limits. The daily monitor is designed to stay well within these limits.
2. **Invalid API Keys**: Double-check your `.env` file and ensure the keys are correct.
3. **Network Issues**: The application will log errors and continue running during temporary network issues.

### Logs

Check `monitor.log` for detailed information about the daily monitoring service, including:
- When checks are performed
- API responses
- New releases found
- Any errors encountered

## Development

### Building

```bash
npm run build
```

### Development Mode

```bash
npm run dev
```

## License

This project is for personal use. Make sure to comply with the terms of service for both Spotify and YouTube APIs.
