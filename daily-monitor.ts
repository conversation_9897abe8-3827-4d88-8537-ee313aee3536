#!/usr/bin/env node
import fetch from 'node-fetch';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import * as dotenv from 'dotenv';
dotenv.config();

// Configuration
const SPOTIFY_CLIENT_ID = process.env.SPOTIFY_CLIENT_ID || "";
const SPOTIFY_CLIENT_SECRET = process.env.SPOTIFY_CLIENT_SECRET || "";
const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY || "";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const DATA_FILE = path.join(__dirname, "last_releases.json");
const LOG_FILE = path.join(__dirname, "monitor.log");

// Interfaces
interface Release {
  id: string;
  name: string;
  release_date: string;
}

interface Video {
  id: string;
  title: string;
  published_at: string;
}

interface LastReleases {
  spotify: { [artistId: string]: Release };
  youtube: { [channelId: string]: Video };
}

// Logging function
const log = (message: string): void => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  console.log(logMessage.trim());
  fs.appendFileSync(LOG_FILE, logMessage);
};

// Load or initialize last checked releases
const loadLastReleases = (): LastReleases => {
  if (fs.existsSync(DATA_FILE)) {
    return JSON.parse(fs.readFileSync(DATA_FILE, 'utf-8')) as LastReleases;
  }
  return { spotify: {}, youtube: {} };
};

// Save last checked releases
const saveLastReleases = (data: LastReleases): void => {
  fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
};

// Get Spotify access token
const getSpotifyToken = async (): Promise<string> => {
  const response = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Basic ${Buffer.from(`${SPOTIFY_CLIENT_ID}:${SPOTIFY_CLIENT_SECRET}`).toString('base64')}`,
    },
    body: 'grant_type=client_credentials',
  });
  const data = await response.json();
  return data.access_token;
};

// Check Spotify for new releases
const checkSpotifyNewReleases = async (artistId: string, token: string): Promise<Release | null> => {
  const response = await fetch(`https://api.spotify.com/v1/artists/${artistId}/albums?album_type=album,single&limit=1`, {
    headers: { Authorization: `Bearer ${token}` },
  });
  const data = await response.json();
  const latestRelease = data.items[0];
  if (!latestRelease) return null;
  return {
    id: latestRelease.id,
    name: latestRelease.name,
    release_date: latestRelease.release_date,
  };
};

// Check YouTube for new videos
const checkYouTubeNewVideos = async (channelId: string): Promise<Video | null> => {
  const response = await fetch(
    `https://www.googleapis.com/youtube/v3/search?part=snippet&channelId=${channelId}&maxResults=1&order=date&key=${YOUTUBE_API_KEY}`
  );
  const data = await response.json();
  const latestVideo = data.items[0];
  if (!latestVideo) return null;
  return {
    id: latestVideo.id.videoId,
    title: latestVideo.snippet.title,
    published_at: latestVideo.snippet.publishedAt,
  };
};

// Console notify with logging
const notify = (title: string, message: string): void => {
  const notification = `\n=== ${title} ===\n${message}\n`;
  console.log(notification);
  log(`NOTIFICATION: ${title} - ${message}`);
};

// Artist configurations
const spotifyArtists = ["2SW5wli9uAsYHQnezGESFB"]; // Rachie
const youtubeChannels = ["UCy2vGg6LqLkFZrJ1WQ2gG6g"]; // Will Stetson

// Main monitoring function
const runDailyCheck = async (): Promise<void> => {
  log("🚀 Starting daily release check");
  let lastReleases = loadLastReleases();
  let hasNewReleases = false;
  
  try {
    const spotifyToken = await getSpotifyToken();
    log("✅ Spotify token obtained");
    
    // Check Spotify
    for (const artistId of spotifyArtists) {
      log(`🔍 Checking Spotify artist: ${artistId}`);
      const release = await checkSpotifyNewReleases(artistId, spotifyToken);
      if (release && (!lastReleases.spotify[artistId] || lastReleases.spotify[artistId].id !== release.id)) {
        notify("🎵 New Spotify Release", `${release.name} released on ${release.release_date}\nSpotify ID: ${release.id}\nSpotify URL: https://open.spotify.com/album/${release.id}`);
        lastReleases.spotify[artistId] = release;
        hasNewReleases = true;
      } else {
        log(`✅ No new Spotify releases for artist ${artistId}`);
      }
    }
    
    // Check YouTube
    for (const channelId of youtubeChannels) {
      log(`🔍 Checking YouTube channel: ${channelId}`);
      const video = await checkYouTubeNewVideos(channelId);
      if (video && (!lastReleases.youtube[channelId] || lastReleases.youtube[channelId].id !== video.id)) {
        notify("🎬 New YouTube Video", `${video.title} uploaded on ${video.published_at}\nVideo URL: https://www.youtube.com/watch?v=${video.id}`);
        lastReleases.youtube[channelId] = video;
        hasNewReleases = true;
      } else {
        log(`✅ No new YouTube videos for channel ${channelId}`);
      }
    }
    
    saveLastReleases(lastReleases);
    
    if (!hasNewReleases) {
      log("✅ Daily check completed - No new releases found");
    } else {
      log("🎉 Daily check completed - New releases found!");
    }
    
  } catch (error) {
    log(`❌ Error during daily check: ${error}`);
    console.error("❌ Error checking for releases:", error);
  }
  
  log("🏁 Daily check finished");
};

// Schedule daily checks (runs every 24 hours)
const startDailyMonitoring = async (): Promise<void> => {
  log("🔄 Starting daily monitoring service");
  
  // Run initial check
  await runDailyCheck();
  
  // Schedule subsequent checks every 24 hours (86400000 ms)
  setInterval(async () => {
    await runDailyCheck();
  }, 24 * 60 * 60 * 1000);
  
  log("⏰ Daily monitoring scheduled - will check every 24 hours");
  console.log("🔄 Daily monitoring is now running. Press Ctrl+C to stop.");
  console.log("📝 Check monitor.log for detailed logs.");
};

// Handle graceful shutdown
process.on('SIGINT', () => {
  log("🛑 Daily monitoring stopped by user");
  console.log("\n🛑 Daily monitoring stopped.");
  process.exit(0);
});

process.on('SIGTERM', () => {
  log("🛑 Daily monitoring terminated");
  console.log("\n🛑 Daily monitoring terminated.");
  process.exit(0);
});

// Start the daily monitoring
startDailyMonitoring().catch((error) => {
  log(`💥 Fatal error in daily monitoring: ${error}`);
  console.error("💥 Fatal error:", error);
  process.exit(1);
});
