{"name": "artist-releases-downloader", "version": "1.0.0", "description": "Monitor artists for new releases on YouTube and Spotify", "type": "module", "main": "main.ts", "scripts": {"start": "bun main.ts", "check-now": "bun check-now.ts", "daily-monitor": "bun daily-monitor.ts", "dev": "bun main.ts"}, "dependencies": {"dotenv": "^17.2.2", "node-fetch": "^2.7.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/node-fetch": "^2.6.13", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "keywords": [], "author": "", "license": "ISC"}